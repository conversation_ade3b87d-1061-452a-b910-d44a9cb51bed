{"name": "log-symbols", "version": "6.0.0", "description": "Colored symbols for various log levels. Example: `✔︎ Success`", "license": "MIT", "repository": "sindresorhus/log-symbols", "funding": "https://github.com/sponsors/sindresorhus", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://sindresorhus.com"}, "type": "module", "exports": {"types": "./index.d.ts", "node": "./index.js", "default": "./browser.js"}, "engines": {"node": ">=18"}, "scripts": {"test": "xo && ava && tsd"}, "files": ["index.js", "index.d.ts", "browser.js"], "keywords": ["unicode", "cli", "cmd", "command-line", "characters", "symbol", "symbols", "figure", "figures", "fallback", "windows", "log", "logging", "terminal", "stdout"], "dependencies": {"chalk": "^5.3.0", "is-unicode-supported": "^1.3.0"}, "devDependencies": {"ava": "^5.3.1", "strip-ansi": "^7.1.0", "tsd": "^0.29.0", "xo": "^0.56.0"}}