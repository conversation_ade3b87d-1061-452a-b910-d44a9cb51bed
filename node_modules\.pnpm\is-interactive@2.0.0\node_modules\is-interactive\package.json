{"name": "is-interactive", "version": "2.0.0", "description": "Check if stdout or stderr is interactive", "license": "MIT", "repository": "sindresorhus/is-interactive", "funding": "https://github.com/sponsors/sindresorhus", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://sindresorhus.com"}, "type": "module", "exports": "./index.js", "engines": {"node": ">=12"}, "scripts": {"test": "xo && ava && tsd"}, "files": ["index.js", "index.d.ts"], "keywords": ["interactive", "stdout", "stderr", "detect", "is", "terminal", "shell", "tty"], "devDependencies": {"@types/node": "^15.0.1", "ava": "^3.15.0", "tsd": "^0.14.0", "xo": "^0.39.1"}}