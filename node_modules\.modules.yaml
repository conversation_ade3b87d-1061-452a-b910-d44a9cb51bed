hoistPattern:
  - '*'
hoistedDependencies:
  '@types/node@24.3.0':
    '@types/node': private
  ansi-regex@6.2.0:
    ansi-regex: private
  chalk@5.6.0:
    chalk: private
  cli-cursor@5.0.0:
    cli-cursor: private
  cli-spinners@2.9.2:
    cli-spinners: private
  emoji-regex@10.4.0:
    emoji-regex: private
  get-east-asian-width@1.3.0:
    get-east-asian-width: private
  is-interactive@2.0.0:
    is-interactive: private
  is-unicode-supported@2.1.0:
    is-unicode-supported: private
  log-symbols@6.0.0:
    log-symbols: private
  mimic-function@5.0.1:
    mimic-function: private
  onetime@7.0.0:
    onetime: private
  restore-cursor@5.1.0:
    restore-cursor: private
  signal-exit@4.1.0:
    signal-exit: private
  stdin-discarder@0.2.2:
    stdin-discarder: private
  string-width@7.2.0:
    string-width: private
  strip-ansi@7.1.0:
    strip-ansi: private
  undici-types@7.10.0:
    undici-types: private
included:
  dependencies: true
  devDependencies: true
  optionalDependencies: true
injectedDeps: {}
layoutVersion: 5
nodeLinker: isolated
packageManager: pnpm@10.14.0
pendingBuilds: []
prunedAt: Tue, 26 Aug 2025 08:13:17 GMT
publicHoistPattern: []
registries:
  '@jsr': https://npm.jsr.io/
  default: https://registry.npmjs.org/
skipped: []
storeDir: C:\Users\<USER>\AppData\Local\pnpm\store\v10
virtualStoreDir: C:\Users\<USER>\workspace\cli\node_modules\.pnpm
virtualStoreDirMaxLength: 60
