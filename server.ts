import { WebSocketServer } from 'ws';

interface UserInfo {
  id: string;       // 唯一 ID
  ip: string;       // IP
  nickname?: string; // 可以在登录/注册时指定
}

const users = new Map<WebSocket, UserInfo>();

const wss = new WebSocketServer({ port: 8080 });

wss.on('connection', (ws, req) => {
  const ip = req.socket.remoteAddress ?? 'unknown';
  // 简单生成一个唯一 ID（可以用 uuid）
  const id = Date.now().toString();

  users.set(ws, { id, ip });

  console.log(`✅ New user connected: ${id} (${ip})`);

  ws.on('message', raw => {
    const message = raw.toString();
    console.log(`Message from ${id}:`, message);

    // 简单实现 "群聊" → 广播给所有人
    for (const client of wss.clients) {
      if (client.readyState === ws.OPEN) {
        client.send(JSON.stringify({
          from: id,
          message,
        }));
      }
    }
  });

  ws.on('close', () => {
    console.log(`❌ User disconnected: ${id}`);
    users.delete(ws);
  });
});
