export class Ws {
  socket: WebSocket;
  constructor() {
    this.socket = new WebSocket('ws://localhost:8080');
  }
  open() {
    this.socket.addEventListener('open', event => {
      console.log('WebSocket connection established!');
      this.socket.send('Hello Server!');
    });
  }
  message() {
    this.socket.addEventListener('message', event => {
      console.log('Message from server: ', event.data);
    });
  }
  close() {
    this.socket.addEventListener('close', event => {
      console.log('WebSocket connection closed:', event.code, event.reason);
    });
  }
  error() {
    this.socket.addEventListener('error', error => {
      console.error('WebSocket error:', error);
    });
  }
}
